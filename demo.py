#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻爬虫演示脚本
展示如何使用不同的新闻爬虫功能
"""

import os
import time
from simple_news_crawler import SimpleNewsCrawler
from advanced_news_crawler import AdvancedNewsCrawler

def demo_simple_crawler():
    """演示简单新闻爬虫"""
    print("=" * 60)
    print("演示：简单新闻爬虫")
    print("=" * 60)
    
    crawler = SimpleNewsCrawler()
    
    # 爬取所有新闻
    print("正在爬取新闻...")
    news_list = crawler.crawl_all_news()
    
    if news_list:
        print(f"成功爬取到 {len(news_list)} 条新闻")
        
        # 显示前5条
        crawler.print_news(news_list, limit=5)
        
        # 保存为JSON
        crawler.save_to_json(news_list, "demo_simple_news.json")
        print("已保存为 demo_simple_news.json")
    else:
        print("未获取到新闻")

def demo_advanced_crawler():
    """演示高级新闻爬虫"""
    print("\n" + "=" * 60)
    print("演示：高级新闻爬虫（关键词过滤）")
    print("=" * 60)
    
    # 使用关键词过滤
    keywords = ["Python", "AI", "machine learning", "programming", "tech"]
    crawler = AdvancedNewsCrawler(keywords=keywords)
    
    print(f"关键词过滤: {', '.join(keywords)}")
    print("正在爬取新闻...")
    
    # 爬取新闻
    news_list = crawler.fetch_from_free_sources()
    
    if news_list:
        print(f"成功爬取到 {len(news_list)} 条相关新闻")
        
        # 显示新闻
        for i, news in enumerate(news_list[:5], 1):
            print(f"\n{i}. 【{news['source']}】{news['title']}")
            print(f"   链接: {news['link']}")
            if news.get('description'):
                print(f"   描述: {news['description'][:100]}...")
        
        # 保存为不同格式
        crawler.save_news(news_list, 'json', 'demo_advanced_news.json')
        crawler.save_news(news_list, 'html', 'demo_advanced_news.html')
        crawler.save_news(news_list, 'markdown', 'demo_advanced_news.md')
        
        print("\n已保存为多种格式:")
        print("- demo_advanced_news.json")
        print("- demo_advanced_news.html")
        print("- demo_advanced_news.md")
    else:
        print("未获取到相关新闻")

def demo_date_filter():
    """演示日期过滤功能"""
    print("\n" + "=" * 60)
    print("演示：日期过滤功能")
    print("=" * 60)
    
    crawler = AdvancedNewsCrawler()
    
    # 获取所有新闻
    all_news = crawler.fetch_from_free_sources()
    print(f"总共获取到 {len(all_news)} 条新闻")
    
    # 过滤最近1天的新闻
    recent_news = crawler.filter_by_date(all_news, days=1)
    print(f"最近1天的新闻: {len(recent_news)} 条")
    
    # 过滤最近7天的新闻
    week_news = crawler.filter_by_date(all_news, days=7)
    print(f"最近7天的新闻: {len(week_news)} 条")

def demo_file_formats():
    """演示不同文件格式的输出"""
    print("\n" + "=" * 60)
    print("演示：不同文件格式输出")
    print("=" * 60)
    
    # 创建示例新闻数据
    sample_news = [
        {
            'title': '示例新闻标题1',
            'link': 'https://example.com/news1',
            'source': '示例新闻源',
            'pub_date': '2025-06-30 14:00:00',
            'description': '这是一条示例新闻的描述内容...',
            'author': '示例作者',
            'crawl_time': '2025-06-30 14:00:00'
        },
        {
            'title': '示例新闻标题2',
            'link': 'https://example.com/news2',
            'source': '另一个新闻源',
            'pub_date': '2025-06-30 13:30:00',
            'description': '这是另一条示例新闻的描述内容...',
            'author': '另一个作者',
            'crawl_time': '2025-06-30 14:00:00'
        }
    ]
    
    crawler = AdvancedNewsCrawler()
    
    # 保存为不同格式
    formats = ['json', 'html', 'markdown']
    for fmt in formats:
        filename = f"demo_format_example.{fmt}"
        crawler.save_news(sample_news, fmt, filename)
        print(f"已生成 {filename}")

def show_generated_files():
    """显示生成的文件"""
    print("\n" + "=" * 60)
    print("生成的文件列表")
    print("=" * 60)
    
    demo_files = [
        'demo_simple_news.json',
        'demo_advanced_news.json',
        'demo_advanced_news.html',
        'demo_advanced_news.md',
        'demo_format_example.json',
        'demo_format_example.html',
        'demo_format_example.md'
    ]
    
    for filename in demo_files:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"✓ {filename} ({size} bytes)")
        else:
            print(f"✗ {filename} (未生成)")

def main():
    """主演示函数"""
    print("新闻爬虫功能演示")
    print("=" * 60)
    
    try:
        # 演示简单爬虫
        demo_simple_crawler()
        time.sleep(2)
        
        # 演示高级爬虫
        demo_advanced_crawler()
        time.sleep(2)
        
        # 演示日期过滤
        demo_date_filter()
        time.sleep(1)
        
        # 演示文件格式
        demo_file_formats()
        time.sleep(1)
        
        # 显示生成的文件
        show_generated_files()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print("\n使用说明:")
        print("1. 简单爬虫: python simple_news_crawler.py")
        print("2. 高级爬虫: python advanced_news_crawler.py --help")
        print("3. 关键词过滤: python advanced_news_crawler.py --keywords Python AI")
        print("4. HTML输出: python advanced_news_crawler.py --format html")
        print("5. 定时爬取: python advanced_news_crawler.py --schedule 6")
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")

if __name__ == "__main__":
    main()
