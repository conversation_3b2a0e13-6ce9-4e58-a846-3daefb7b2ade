# 蚂蚁庄园 - 养小鸡游戏

一个模仿蚂蚁庄园的前端小游戏，包含养小鸡、喂食、收蛋等互动功能。

## 功能特点

### 🐣 小鸡养成系统
- **多状态小鸡**: 饥饿、开心、睡觉等不同状态
- **等级系统**: 通过喂食和互动提升小鸡等级
- **智能行为**: 小鸡会根据状态自动表现不同行为
- **外观变化**: 不同等级的小鸡有不同颜色

### 🌾 喂食系统
- **饲料管理**: 饲料会随时间自动恢复
- **喂食动画**: 精美的饲料掉落动画效果
- **状态提升**: 喂食可以增加小鸡的饥饿度和开心度

### 🥚 收蛋系统
- **自动生蛋**: 小鸡在满足条件时会自动生蛋
- **收集动画**: 点击鸡蛋收集时的动画效果
- **数量统计**: 实时显示收集的鸡蛋数量

### 💾 数据持久化
- **自动保存**: 游戏进度自动保存到本地
- **版本升级**: 支持存档数据版本升级
- **备份恢复**: 自动创建备份，支持数据恢复

### 📱 响应式设计
- **移动端适配**: 完美支持手机和平板设备
- **触摸优化**: 针对触摸设备优化的交互体验
- **流畅动画**: 使用CSS3实现的流畅动画效果

## 游戏玩法

### 基础操作
1. **喂食小鸡**: 点击"喂食"按钮给小鸡喂食
2. **收集鸡蛋**: 点击地面上的鸡蛋进行收集
3. **查看状态**: 点击"信息"按钮查看小鸡详细状态
4. **互动小鸡**: 直接点击小鸡增加开心度

### 游戏机制
- **饥饿度**: 影响小鸡的心情和生蛋能力
- **开心度**: 影响小鸡的行为和经验获取
- **经验值**: 通过喂食和互动获得，用于升级
- **饲料恢复**: 每分钟自动恢复1个饲料

### 升级系统
- 小鸡通过获得经验值升级
- 不同等级的小鸡有不同的外观颜色
- 高等级小鸡生蛋效率更高

## 技术实现

### 前端技术栈
- **HTML5**: 页面结构和语义化标签
- **CSS3**: 样式设计和动画效果
- **JavaScript ES6+**: 游戏逻辑和交互功能
- **localStorage**: 本地数据存储

### 项目结构
```
ant_farm/
├── index.html          # 主页面
├── css/
│   ├── style.css       # 主样式文件
│   └── animations.css  # 动画样式
├── js/
│   ├── game.js         # 游戏主逻辑
│   ├── chicken.js      # 小鸡系统
│   └── storage.js      # 存储管理
└── README.md           # 说明文档
```

### 核心类说明
- **GameSystem**: 游戏主系统，管理整体游戏流程
- **ChickenSystem**: 小鸡系统，处理小鸡的状态和行为
- **StorageSystem**: 存储系统，处理数据的保存和加载

## 使用方法

### 直接运行
1. 下载所有文件到本地
2. 用浏览器打开 `index.html` 文件
3. 开始游戏！

### 本地服务器运行（推荐）
```bash
# 使用Python启动本地服务器
cd ant_farm
python -m http.server 8000

# 或使用Node.js
npx http-server

# 然后在浏览器访问 http://localhost:8000
```

## 浏览器兼容性

### 支持的浏览器
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

### 移动端支持
- ✅ iOS Safari 12+
- ✅ Android Chrome 60+
- ✅ 微信内置浏览器

## 开发说明

### 自定义配置
可以通过修改以下参数来调整游戏体验：

```javascript
// 在 chicken.js 中调整小鸡属性
this.chicken = {
    hunger: 80,        // 初始饥饿度
    happiness: 70,     // 初始开心度
    // ...
};

// 在 game.js 中调整游戏参数
this.gameData = {
    feedCount: 100,    // 初始饲料数量
    // ...
};
```

### 添加新功能
1. 在对应的类中添加新方法
2. 在 HTML 中添加相应的 UI 元素
3. 在 CSS 中添加样式
4. 绑定事件处理器

## 更新日志

### v1.0.0 (2025-08-22)
- ✨ 初始版本发布
- 🐣 完整的小鸡养成系统
- 🌾 喂食和收蛋功能
- 💾 数据持久化存储
- 📱 响应式设计支持

## 许可证

MIT License - 可自由使用和修改

## 贡献

欢迎提交 Issue 和 Pull Request！

---

**享受养小鸡的乐趣吧！** 🐣✨
