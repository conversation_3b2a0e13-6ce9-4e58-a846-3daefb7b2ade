# 热门新闻爬虫程序

这是一个用Python编写的新闻爬虫程序，可以从多个新闻源爬取当前热门新闻。

## 功能特点

- 支持多个新闻源爬取
- 支持RSS新闻源
- 支持GitHub热门项目
- 支持Hacker News热门文章
- 支持保存为JSON和CSV格式
- 完整的日志记录
- 错误处理和重试机制

## 文件说明

1. **news_crawler.py** - 完整版新闻爬虫
   - 支持新浪、网易、腾讯等主流新闻网站
   - 使用BeautifulSoup解析HTML
   - 功能更全面但可能受到反爬虫限制

2. **simple_news_crawler.py** - 简化版新闻爬虫（推荐）
   - 使用RSS源和公开API
   - 更稳定，不容易被封禁
   - 包含GitHub热门项目和Hacker News

3. **advanced_news_crawler.py** - 高级新闻爬虫（功能最全）
   - 支持关键词过滤
   - 支持多种输出格式（JSON、HTML、Markdown）
   - 支持日期过滤
   - 支持定时爬取
   - 支持命令行参数

4. **demo.py** - 演示脚本
   - 展示各种爬虫功能的使用方法
   - 包含完整的使用示例

5. **requirements.txt** - 依赖包列表

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 运行完整版爬虫
```bash
python news_crawler.py
```

### 运行简化版爬虫（推荐）
```bash
python simple_news_crawler.py
```

### 运行高级版爬虫
```bash
# 基本使用
python advanced_news_crawler.py

# 使用关键词过滤
python advanced_news_crawler.py --keywords Python AI 机器学习

# 输出为HTML格式
python advanced_news_crawler.py --format html

# 输出为Markdown格式
python advanced_news_crawler.py --format markdown

# 过滤最近3天的新闻
python advanced_news_crawler.py --days 3

# 定时爬取（每6小时一次）
python advanced_news_crawler.py --schedule 6
```

### 运行演示脚本
```bash
python demo.py
```

## 程序功能

### 完整版爬虫功能
1. 爬取所有新闻源
2. 爬取新浪新闻
3. 爬取网易新闻
4. 爬取腾讯新闻

### 简化版爬虫功能
- 自动爬取多个RSS新闻源
- 获取GitHub热门项目
- 获取Hacker News热门文章
- 支持多种保存格式

## 输出格式

程序会显示爬取到的新闻列表，包含：
- 新闻标题
- 新闻链接
- 新闻源
- 发布时间
- 爬取时间
- 新闻描述（如果有）

## 保存选项

- **JSON格式**: 结构化数据，便于程序处理
- **CSV格式**: 表格格式，便于Excel打开
- **both**: 同时保存两种格式

## 注意事项

1. **网络连接**: 确保网络连接正常
2. **访问频率**: 程序已内置延时，避免过于频繁的请求
3. **反爬虫**: 某些网站可能有反爬虫机制，建议使用简化版
4. **数据准确性**: 爬取的数据仅供参考，请以官方发布为准

## 日志文件

程序运行时会生成 `news_crawler.log` 日志文件，记录：
- 爬取过程
- 错误信息
- 成功获取的新闻数量

## 自定义配置

你可以修改程序中的以下配置：
- 新闻源URL
- 爬取数量限制
- 保存文件名格式
- 请求头信息

## 示例输出

```
================================================================================
热门新闻汇总 (共25条，显示前20条)
================================================================================

1. 【GitHub】GitHub热门: awesome-python - A curated list of awesome Python frameworks...
   链接: https://github.com/vinta/awesome-python
   描述: A curated list of awesome Python frameworks, libraries, software and resources
   发布时间: 2024-01-15T10:30:00Z
   爬取时间: 2024-06-30 15:30:45

2. 【Hacker News】HN热门: Show HN: I built a tool to visualize code complexity
   链接: https://example.com/tool
   描述: 评分: 245, 评论: 67
   发布时间: 2024-06-30 14:25:30
   爬取时间: 2024-06-30 15:30:45
```

## 故障排除

1. **网络超时**: 检查网络连接，可能需要使用代理
2. **解析失败**: 网站结构可能发生变化，需要更新解析规则
3. **编码问题**: 确保系统支持UTF-8编码

## 扩展功能

你可以根据需要添加：
- 更多新闻源
- 关键词过滤
- 新闻分类
- 定时爬取
- 数据库存储
- Web界面

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的robots.txt和使用条款。
