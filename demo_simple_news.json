[{"title": "学习卡丨“人不负青山，青山定不负人”", "link": "http://politics.people.com.cn/n1/2025/0605/c1001-40494898.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "镜观·足迹｜呵护千山万水 擘画永续发展", "link": "http://politics.people.com.cn/n1/2025/0605/c1001-40494899.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "学习手记 | “环境就是民生”", "link": "http://politics.people.com.cn/n1/2025/0605/c1001-40494900.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "刘国中在安徽调研时强调 全力以赴夺取夏粮丰收 全面打牢秋粮生产基础", "link": "http://politics.people.com.cn/n1/2025/0605/c1001-40494881.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "韩正会见中美高级别二轨对话美方代表团", "link": "http://politics.people.com.cn/n1/2025/0605/c1001-40494879.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "国务院印发《关于开展第四次全国农业普查的通知》", "link": "http://politics.people.com.cn/n1/2025/0605/c1001-40494878.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "中央军委主席习近平签署命令 发布《军事设施建设条例》", "link": "http://politics.people.com.cn/n1/2025/0605/c1024-40494849.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "丁薛祥在山西调研时强调 守好公平公正“生命线” 确保实现“平安高考”目标", "link": "http://politics.people.com.cn/n1/2025/0605/c1024-40494847.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "中共中央办公厅印发《党组讨论和决定党员处分事项工作程序规定》", "link": "http://politics.people.com.cn/n1/2025/0605/c1001-40494813.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "全国人大常委会启动节约能源法执法检查", "link": "http://politics.people.com.cn/n1/2025/0605/c1001-40494812.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "【文脉华章】非遗焕新 生生不息丨用太极招式来炒茶！西湖龙井如何“撩”动全世界的心？", "link": "http://politics.people.com.cn/n1/2025/0605/c1001-40494792.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "天天学习丨中南海月刊（2025.05）", "link": "http://politics.people.com.cn/n1/2025/0605/c1001-40494795.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "习语丨“人不负青山，青山定不负人”", "link": "http://politics.people.com.cn/n1/2025/0605/c1001-40494806.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "韩正将赴法国出席联合国海洋大会并访问西班牙", "link": "http://politics.people.com.cn/n1/2025/0605/c1001-40494772.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "美丽中国我先行｜“植”此青绿", "link": "http://politics.people.com.cn/n1/2025/0605/c1001-40494642.html", "source": "people", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "微视频｜“新”在中国", "link": "http://www.news.cn/politics/2022-12/14/c_1129207254.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "疫情防控措施优化 各地复商复市进行时", "link": "http://www.news.cn/politics/2022-12/14/c_1129207168.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "国家卫健委发布新冠病毒疫苗第二剂次加强免疫接种实施方案", "link": "http://www.news.cn/politics/2022-12/14/c_1129207117.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "心血管病患者一旦感染新冠病毒，居家应该注意什么，如何避免基础病加重，何时需要去就医……【科学防疫小贴士】", "link": "http://www.news.cn/politics/2022-12/14/c_1129207057.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "老年人现在打疫苗晚不晚？受基础病影响的能不能打？独居老人是否有必要接种？【科学防疫小贴士】", "link": "http://www.news.cn/politics/2022-12/14/c_1129207047.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "为什么不再公布无症状感染者相关信息？中疾控专家解读", "link": "http://www.news.cn/politics/2022-12/14/c_1129207083.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "关于BQ.1，你需要知道的几件事——", "link": "http://www.news.cn/politics/2022-12/14/c_1129206954.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "6年、10年、12年，他们都经历了什么？", "link": "http://www.news.cn/politics/2022-12/14/c_1129206833.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "习近平将向《生物多样性公约》第十五次缔约方大会第二阶段高级别会议开幕式致辞", "link": "http://www.news.cn/politics/2022-12/14/c_1129206906.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "明起新一轮冷空气接踵而至 我国大部将迎大风降温", "link": "http://www.news.cn/politics/2022-12/14/c_1129206671.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "学习进行时｜二十大后重要外交活动，这些关键词习近平总书记反复提及", "link": "http://www.news.cn/politics/2022-12/14/c_1129206651.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "加强和完善现代金融监管（认真学习宣传贯彻党的二十大精神）", "link": "http://www.news.cn/politics/2022-12/14/c_1129206432.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "北京今天最高温0℃山区有零星小雪 明起三天冷空气又将来降温", "link": "http://www.news.cn/politics/2022-12/14/c_1129206415.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "2亿年前蝈蝈嗓门有多高？化石研究得出答案", "link": "http://www.news.cn/politics/2022-12/14/c_1129206396.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "中共中央办公厅 国务院办公厅印发《乡村振兴责任制实施办法》", "link": "http://www.news.cn/politics/2022-12/14/c_1129206017.htm", "source": "xinhua", "pub_date": "", "description": "", "crawl_time": "2025-06-30 14:10:28"}, {"title": "GitHub热门: DeepSeek-V3 - ", "link": "https://github.com/deepseek-ai/DeepSeek-V3", "source": "GitHub", "pub_date": "2024-12-26T09:52:40Z", "description": "", "crawl_time": "2025-06-30 14:10:30"}, {"title": "GitHub热门: DeepSeek-R1 - ", "link": "https://github.com/deepseek-ai/DeepSeek-R1", "source": "GitHub", "pub_date": "2025-01-20T11:57:28Z", "description": "", "crawl_time": "2025-06-30 14:10:30"}, {"title": "GitHub热门: browser-use - 🌐 Make websites accessible for AI agents. Automate tasks online with ease.", "link": "https://github.com/browser-use/browser-use", "source": "GitHub", "pub_date": "2024-10-31T16:00:56Z", "description": "🌐 Make websites accessible for AI agents. Automate tasks online with ease.", "crawl_time": "2025-06-30 14:10:30"}, {"title": "GitHub热门: system-prompts-and-models-of-ai-tools - FULL v0, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>.dev, <PERSON><PERSON>, <PERSON>, Replit Agent, Windsurf Agent, VSCode Agent, Dia Br", "link": "https://github.com/x1xhlol/system-prompts-and-models-of-ai-tools", "source": "GitHub", "pub_date": "2025-03-05T16:38:29Z", "description": "FULL v0, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>.dev, <PERSON><PERSON>, <PERSON>, Replit Agent, Windsurf Agent, VSCode Agent, <PERSON>a Browser, Trae AI & Cluely (And other Open Sourced) System Prompts, Tools & AI Models.", "crawl_time": "2025-06-30 14:10:30"}, {"title": "GitHub热门: FreeDomain - DigitalPlat FreeDomain: Free Domain For Everyone", "link": "https://github.com/DigitalPlatDev/FreeDomain", "source": "GitHub", "pub_date": "2024-05-30T13:23:00Z", "description": "DigitalPlat FreeDomain: Free Domain For Everyone", "crawl_time": "2025-06-30 14:10:30"}, {"title": "GitHub热门: markitdown - Python tool for converting files and office documents to Markdown.", "link": "https://github.com/microsoft/markitdown", "source": "GitHub", "pub_date": "2024-11-13T19:56:40Z", "description": "Python tool for converting files and office documents to Markdown.", "crawl_time": "2025-06-30 14:10:30"}, {"title": "GitHub热门: OpenHands - 🙌 OpenHands: Code Less, Make More", "link": "https://github.com/All-Hands-AI/OpenHands", "source": "GitHub", "pub_date": "2024-03-13T03:33:31Z", "description": "🙌 OpenHands: Code Less, Make More", "crawl_time": "2025-06-30 14:10:30"}, {"title": "GitHub热门: awesome-mcp-servers - A collection of MCP servers.", "link": "https://github.com/punkpeye/awesome-mcp-servers", "source": "GitHub", "pub_date": "2024-11-30T04:49:10Z", "description": "A collection of MCP servers.", "crawl_time": "2025-06-30 14:10:30"}, {"title": "GitHub热门: servers - Model Context Protocol Servers", "link": "https://github.com/modelcontextprotocol/servers", "source": "GitHub", "pub_date": "2024-11-19T01:10:17Z", "description": "Model Context Protocol Servers", "crawl_time": "2025-06-30 14:10:30"}, {"title": "GitHub热门: grok-1 - Grok open release", "link": "https://github.com/xai-org/grok-1", "source": "GitHub", "pub_date": "2024-03-17T08:53:38Z", "description": "Grok open release", "crawl_time": "2025-06-30 14:10:30"}, {"title": "HN热门: Bought an Ampere Altra System", "link": "https://marcin.juszkiewicz.com.pl/2025/06/27/bought-myself-an-ampere-altra-system/", "source": "Hacker News", "pub_date": "2025-06-30 12:38:49", "description": "评分: 41, 评论: 4", "crawl_time": "2025-06-30 14:10:31"}, {"title": "HN热门: Gridfinity: The modular, open-source grid storage system", "link": "https://gridfinity.xyz/", "source": "Hacker News", "pub_date": "2025-06-30 11:37:21", "description": "评分: 45, 评论: 3", "crawl_time": "2025-06-30 14:10:31"}, {"title": "HN热门: NativeJIT: A C++ expression –> x64 JIT", "link": "https://github.com/BitFunnel/NativeJIT", "source": "Hacker News", "pub_date": "2025-06-30 10:59:52", "description": "评分: 23, 评论: 6", "crawl_time": "2025-06-30 14:10:31"}, {"title": "HN热门: I made my VM think it has a CPU fan", "link": "https://wbenny.github.io/2025/06/29/i-made-my-vm-think-it-has-a-cpu-fan.html", "source": "Hacker News", "pub_date": "2025-06-29 21:55:18", "description": "评分: 483, 评论: 117", "crawl_time": "2025-06-30 14:10:31"}, {"title": "HN热门: Continuous Glucose Monitoring", "link": "https://www.imperialviolet.org/2025/06/29/cgm.html", "source": "Hacker News", "pub_date": "2025-06-30 10:09:30", "description": "评分: 58, 评论: 25", "crawl_time": "2025-06-30 14:10:31"}, {"title": "HN热门: The Book of Shaders", "link": "https://thebookofshaders.com/", "source": "Hacker News", "pub_date": "2025-06-27 01:48:03", "description": "评分: 92, 评论: 14", "crawl_time": "2025-06-30 14:10:32"}, {"title": "HN热门: Amber insect fossils reveal \"zombie\" fungi likely lived alongside dinosaurs", "link": "https://www.cnn.com/2025/06/24/science/amber-insect-zombie-fungi-fossil", "source": "Hacker News", "pub_date": "2025-06-27 05:25:45", "description": "评分: 30, 评论: 8", "crawl_time": "2025-06-30 14:10:33"}, {"title": "HN热门: Cell Towers Can Double as Cheap Radar Systems for Ports and Harbors (2014)", "link": "https://spectrum.ieee.org/cell-tower-signals-can-improve-port-security", "source": "Hacker News", "pub_date": "2025-06-30 05:48:12", "description": "评分: 70, 评论: 29", "crawl_time": "2025-06-30 14:10:33"}, {"title": "HN热门: Touching the back wall of the Apple store", "link": "https://blog.lauramichet.com/touching-the-back-wall-of-the-apple-store/", "source": "Hacker News", "pub_date": "2025-06-27 10:45:02", "description": "评分: 116, 评论: 83", "crawl_time": "2025-06-30 14:10:33"}, {"title": "HN热门: Ask HN: What Are You Working On? (June 2025)", "link": "https://news.ycombinator.com/item?id=44416093", "source": "Hacker News", "pub_date": "2025-06-30 04:21:28", "description": "评分: 150, 评论: 498", "crawl_time": "2025-06-30 14:10:34"}]