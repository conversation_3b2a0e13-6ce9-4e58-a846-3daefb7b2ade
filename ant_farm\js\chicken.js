// 小鸡系统管理类
class ChickenSystem {
    constructor() {
        this.chicken = {
            level: 1,
            experience: 0,
            hunger: 80,        // 饥饿度 (0-100)
            happiness: 70,     // 开心度 (0-100)
            energy: 90,        // 精力值 (0-100)
            lastFeedTime: Date.now(),
            lastEggTime: Date.now(),
            totalEggs: 0,
            state: 'idle'      // idle, eating, happy, laying, sleeping
        };
        
        this.chickenElement = null;
        this.statusUpdateInterval = null;
        this.behaviorInterval = null;
        
        this.init();
    }
    
    init() {
        this.chickenElement = document.getElementById('chicken');
        this.startStatusUpdates();
        this.startBehaviorLoop();
        this.updateDisplay();
    }
    
    // 开始状态更新循环
    startStatusUpdates() {
        this.statusUpdateInterval = setInterval(() => {
            this.updateStatus();
            this.updateDisplay();
        }, 5000); // 每5秒更新一次状态
    }
    
    // 开始行为循环
    startBehaviorLoop() {
        this.behaviorInterval = setInterval(() => {
            this.randomBehavior();
        }, 8000); // 每8秒执行一次随机行为
    }
    
    // 更新小鸡状态
    updateStatus() {
        const now = Date.now();
        const timeSinceLastFeed = now - this.chicken.lastFeedTime;
        
        // 饥饿度随时间增加
        if (timeSinceLastFeed > 30000) { // 30秒后开始饿
            this.chicken.hunger = Math.max(0, this.chicken.hunger - 1);
        }
        
        // 开心度受饥饿度影响
        if (this.chicken.hunger < 30) {
            this.chicken.happiness = Math.max(0, this.chicken.happiness - 2);
        } else if (this.chicken.hunger > 70) {
            this.chicken.happiness = Math.min(100, this.chicken.happiness + 1);
        }
        
        // 精力值恢复
        if (this.chicken.state === 'sleeping') {
            this.chicken.energy = Math.min(100, this.chicken.energy + 5);
        } else {
            this.chicken.energy = Math.max(0, this.chicken.energy - 0.5);
        }
        
        // 检查是否需要生蛋
        this.checkEggLaying();
        
        // 更新小鸡外观
        this.updateAppearance();
    }
    
    // 喂食小鸡
    feed() {
        if (this.chicken.hunger >= 100) {
            return { success: false, message: '小鸡已经很饱了！' };
        }
        
        // 增加饥饿度和开心度
        this.chicken.hunger = Math.min(100, this.chicken.hunger + 25);
        this.chicken.happiness = Math.min(100, this.chicken.happiness + 15);
        this.chicken.experience += 5;
        this.chicken.lastFeedTime = Date.now();
        
        // 播放吃食动画
        this.playAnimation('eating');
        
        // 检查升级
        this.checkLevelUp();
        
        this.updateDisplay();
        
        return { success: true, message: '小鸡吃得很开心！' };
    }
    
    // 检查升级
    checkLevelUp() {
        const expNeeded = this.chicken.level * 100;
        if (this.chicken.experience >= expNeeded) {
            this.chicken.level++;
            this.chicken.experience -= expNeeded;
            this.playAnimation('happy');
            return true;
        }
        return false;
    }
    
    // 检查生蛋
    checkEggLaying() {
        const now = Date.now();
        const timeSinceLastEgg = now - this.chicken.lastEggTime;
        
        // 条件：饥饿度>50，开心度>60，距离上次生蛋超过60秒
        if (this.chicken.hunger > 50 && 
            this.chicken.happiness > 60 && 
            timeSinceLastEgg > 60000) {
            
            // 随机生蛋概率
            if (Math.random() < 0.3) {
                this.layEgg();
            }
        }
    }
    
    // 生蛋
    layEgg() {
        this.chicken.lastEggTime = Date.now();
        this.chicken.totalEggs++;
        this.chicken.experience += 10;
        
        // 播放生蛋动画
        this.playAnimation('laying');
        
        // 通知游戏系统生成鸡蛋
        if (window.gameSystem) {
            window.gameSystem.addEgg();
        }
        
        return true;
    }
    
    // 随机行为
    randomBehavior() {
        if (this.chicken.state !== 'idle') return;
        
        const behaviors = [];
        
        // 根据状态决定可能的行为
        if (this.chicken.hunger < 30) {
            behaviors.push('hungry');
        }
        
        if (this.chicken.energy < 20) {
            behaviors.push('sleepy');
        }
        
        if (this.chicken.happiness > 80) {
            behaviors.push('excited');
        }
        
        // 默认行为
        behaviors.push('idle', 'look_around');
        
        const behavior = behaviors[Math.floor(Math.random() * behaviors.length)];
        this.setBehavior(behavior);
    }
    
    // 设置行为状态
    setBehavior(behavior) {
        this.chicken.state = behavior;
        this.updateAppearance();
        
        // 行为持续时间
        setTimeout(() => {
            if (this.chicken.state === behavior) {
                this.chicken.state = 'idle';
                this.updateAppearance();
            }
        }, 3000);
    }
    
    // 播放动画
    playAnimation(animationType) {
        if (!this.chickenElement) return;
        
        // 移除现有动画类
        this.chickenElement.classList.remove('eating', 'happy', 'laying', 'hungry', 'sleepy', 'excited');
        
        // 添加新动画类
        this.chickenElement.classList.add(animationType);
        
        // 设置状态
        this.chicken.state = animationType;
        
        // 动画结束后恢复idle状态
        setTimeout(() => {
            this.chickenElement.classList.remove(animationType);
            this.chicken.state = 'idle';
            this.updateAppearance();
        }, animationType === 'laying' ? 2000 : 1500);
    }
    
    // 更新外观
    updateAppearance() {
        if (!this.chickenElement) return;
        
        // 移除所有状态类
        this.chickenElement.classList.remove('hungry', 'sleepy', 'excited');
        
        // 根据状态添加对应类
        if (this.chicken.hunger < 30) {
            this.chickenElement.classList.add('hungry');
        }
        
        if (this.chicken.energy < 20) {
            this.chickenElement.classList.add('sleepy');
        }
        
        if (this.chicken.happiness > 80 && this.chicken.energy > 60) {
            this.chickenElement.classList.add('excited');
        }
        
        // 更新小鸡颜色（根据等级）
        this.updateChickenColor();
    }
    
    // 更新小鸡颜色
    updateChickenColor() {
        const body = this.chickenElement.querySelector('.chicken-body');
        const head = this.chickenElement.querySelector('.chicken-head');
        
        if (!body || !head) return;
        
        let color = '#FFD700'; // 默认金色
        
        if (this.chicken.level >= 5) {
            color = '#FF6B6B'; // 红色
        } else if (this.chicken.level >= 3) {
            color = '#4ECDC4'; // 青色
        }
        
        body.style.background = color;
        head.style.background = color;
    }
    
    // 更新显示
    updateDisplay() {
        // 更新进度条
        this.updateProgressBar('hungerBar', this.chicken.hunger);
        this.updateProgressBar('happinessBar', this.chicken.happiness);
        this.updateProgressBar('expBar', (this.chicken.experience / (this.chicken.level * 100)) * 100);
        
        // 更新等级显示
        const levelElement = document.getElementById('chickenLevel');
        if (levelElement) {
            levelElement.textContent = this.chicken.level;
        }
    }
    
    // 更新进度条
    updateProgressBar(barId, percentage) {
        const bar = document.getElementById(barId);
        if (bar) {
            bar.style.width = `${Math.max(0, Math.min(100, percentage))}%`;
        }
    }
    
    // 获取小鸡状态
    getStatus() {
        return { ...this.chicken };
    }
    
    // 设置小鸡状态（用于加载存档）
    setStatus(status) {
        this.chicken = { ...this.chicken, ...status };
        this.updateDisplay();
        this.updateAppearance();
    }
    
    // 点击小鸡交互
    onChickenClick() {
        if (this.chicken.state !== 'idle') return;
        
        // 增加一点开心度
        this.chicken.happiness = Math.min(100, this.chicken.happiness + 5);
        
        // 播放开心动画
        this.playAnimation('happy');
        
        this.updateDisplay();
        
        // 显示提示
        if (window.gameSystem) {
            window.gameSystem.showMessage('小鸡很开心！+5 开心度');
        }
    }
    
    // 销毁
    destroy() {
        if (this.statusUpdateInterval) {
            clearInterval(this.statusUpdateInterval);
        }
        if (this.behaviorInterval) {
            clearInterval(this.behaviorInterval);
        }
    }
}
