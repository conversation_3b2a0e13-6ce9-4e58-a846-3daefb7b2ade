/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
    background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
    height: 100vh;
    overflow: hidden;
    user-select: none;
}

/* 游戏主容器 */
.game-container {
    width: 100%;
    height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
}

/* 顶部状态栏 */
.status-bar {
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 20px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 16px;
    font-weight: bold;
}

.status-item .icon {
    font-size: 20px;
}

.status-item .value {
    color: #ff6b35;
    min-width: 40px;
    text-align: center;
}

/* 农场主区域 */
.farm-area {
    flex: 1;
    position: relative;
    overflow: hidden;
    background: linear-gradient(to bottom, #87CEEB 0%, #87CEEB 60%, #98FB98 60%, #228B22 100%);
}

/* 天空背景 */
.sky {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60%;
    background: linear-gradient(to bottom, #87CEEB, #B0E0E6);
}

/* 太阳 */
.sun {
    position: absolute;
    top: 20px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, #FFD700, #FFA500);
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
}

/* 云朵 */
.cloud {
    position: absolute;
    background: white;
    border-radius: 50px;
    opacity: 0.8;
}

.cloud::before,
.cloud::after {
    content: '';
    position: absolute;
    background: white;
    border-radius: 50px;
}

.cloud1 {
    width: 80px;
    height: 30px;
    top: 40px;
    left: 20%;
    animation: float 20s infinite linear;
}

.cloud1::before {
    width: 40px;
    height: 40px;
    top: -20px;
    left: 10px;
}

.cloud1::after {
    width: 60px;
    height: 35px;
    top: -15px;
    right: 10px;
}

.cloud2 {
    width: 60px;
    height: 25px;
    top: 80px;
    left: 70%;
    animation: float 25s infinite linear;
}

.cloud2::before {
    width: 30px;
    height: 30px;
    top: -15px;
    left: 5px;
}

.cloud2::after {
    width: 45px;
    height: 28px;
    top: -12px;
    right: 5px;
}

/* 草地 */
.grass {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40%;
    background: linear-gradient(to bottom, #98FB98, #228B22);
    background-image: 
        radial-gradient(circle at 20% 80%, #32CD32 2px, transparent 2px),
        radial-gradient(circle at 80% 90%, #32CD32 1px, transparent 1px),
        radial-gradient(circle at 40% 85%, #90EE90 1.5px, transparent 1.5px);
    background-size: 50px 50px, 30px 30px, 40px 40px;
}

/* 小鸡区域 */
.chicken-area {
    position: absolute;
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 200px;
}

/* 小鸡样式 */
.chicken {
    position: absolute;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
}

.chicken-body {
    position: relative;
    width: 60px;
    height: 50px;
    background: #FFD700;
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    border: 2px solid #FFA500;
}

.chicken-head {
    position: absolute;
    top: -25px;
    left: 10px;
    width: 40px;
    height: 35px;
    background: #FFD700;
    border-radius: 50%;
    border: 2px solid #FFA500;
}

.chicken-eye {
    position: absolute;
    width: 6px;
    height: 6px;
    background: #000;
    border-radius: 50%;
    top: 12px;
}

.left-eye {
    left: 8px;
}

.right-eye {
    right: 8px;
}

.chicken-beak {
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #FF8C00;
}

.chicken-wing {
    position: absolute;
    top: 10px;
    right: -5px;
    width: 20px;
    height: 25px;
    background: #FFA500;
    border-radius: 50% 0 50% 50%;
    border: 1px solid #FF8C00;
}

.chicken-tail {
    position: absolute;
    top: 5px;
    right: -15px;
    width: 15px;
    height: 20px;
    background: #FF8C00;
    border-radius: 0 50% 50% 0;
    border: 1px solid #FF6347;
}

.chicken-legs {
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 20px;
}

.chicken-leg {
    width: 3px;
    height: 15px;
    background: #FF8C00;
    position: relative;
}

.chicken-leg::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: -2px;
    width: 7px;
    height: 3px;
    background: #FF8C00;
    border-radius: 0 0 50% 50%;
}

/* 控制面板 */
.control-panel {
    background: rgba(255, 255, 255, 0.95);
    padding: 15px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 15px 20px;
    border: none;
    border-radius: 15px;
    background: linear-gradient(145deg, #f0f0f0, #d0d0d0);
    box-shadow: 5px 5px 10px #bebebe, -5px -5px 10px #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 7px 7px 15px #bebebe, -7px -7px 15px #ffffff;
}

.action-btn:active {
    transform: translateY(0);
    box-shadow: inset 3px 3px 6px #bebebe, inset -3px -3px 6px #ffffff;
}

.btn-icon {
    font-size: 24px;
}

.btn-text {
    font-size: 14px;
    font-weight: bold;
    color: #333;
}

.feed-btn:hover {
    background: linear-gradient(145deg, #90EE90, #7FDD7F);
}

.collect-btn:hover {
    background: linear-gradient(145deg, #FFE4B5, #F0D0A0);
}

.info-btn:hover {
    background: linear-gradient(145deg, #87CEEB, #76BDDA);
}

/* 鸡蛋容器 */
.eggs-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.egg {
    position: absolute;
    width: 20px;
    height: 25px;
    background: linear-gradient(145deg, #FFF8DC, #F5DEB3);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    border: 1px solid #DDD;
    cursor: pointer;
    pointer-events: all;
    transition: transform 0.2s ease;
}

.egg:hover {
    transform: scale(1.1);
}

/* 饲料效果 */
.feed-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.feed-particle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #8B4513;
    border-radius: 50%;
    pointer-events: none;
}

/* 信息面板 */
.info-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.info-content {
    background: white;
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 300px;
    width: 90%;
}

.info-content h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
    font-size: 20px;
}

.info-item {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-item span {
    min-width: 60px;
    font-weight: bold;
    color: #666;
}

.progress-bar {
    flex: 1;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #ddd;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 80%;
}

#hungerBar {
    background: linear-gradient(90deg, #FF6B6B, #FF8E53);
}

#happinessBar {
    background: linear-gradient(90deg, #4ECDC4, #44A08D);
}

#expBar {
    background: linear-gradient(90deg, #A8E6CF, #7FCDCD);
}

.close-btn {
    width: 100%;
    padding: 10px;
    margin-top: 20px;
    border: none;
    border-radius: 10px;
    background: linear-gradient(145deg, #f0f0f0, #d0d0d0);
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: linear-gradient(145deg, #e0e0e0, #c0c0c0);
}

/* 消息提示 */
.message-toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 15px 25px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    z-index: 2000;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.message-toast.show {
    opacity: 1;
}

/* 触摸优化 */
.action-btn, .chicken, .egg {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

/* 禁用文本选择 */
.game-container, .action-btn, .status-bar {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* 加载动画 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #ff6b35;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .status-bar {
        padding: 8px 15px;
        font-size: 14px;
    }

    .status-item .icon {
        font-size: 18px;
    }

    .control-panel {
        padding: 10px;
    }

    .action-btn {
        padding: 12px 15px;
        gap: 3px;
        min-height: 60px;
        min-width: 60px;
    }

    .btn-icon {
        font-size: 20px;
    }

    .btn-text {
        font-size: 12px;
    }

    .chicken-area {
        width: 150px;
        height: 150px;
    }

    .info-content {
        padding: 20px;
        max-width: 280px;
    }

    .chicken {
        transform: translateX(-50%) scale(0.8);
    }
}

@media (max-width: 480px) {
    .status-bar {
        flex-direction: column;
        gap: 5px;
        padding: 10px;
    }

    .control-panel {
        flex-direction: row;
        gap: 10px;
    }

    .action-btn {
        flex: 1;
        padding: 10px;
    }
}
