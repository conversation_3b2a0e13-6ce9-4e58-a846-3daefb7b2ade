#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级新闻爬虫程序
支持关键词过滤、定时爬取、多种输出格式
"""

import requests
import json
import time
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
import argparse
import schedule
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('advanced_news.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class AdvancedNewsCrawler:
    def __init__(self, keywords: List[str] = None):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.keywords = keywords or []
        
        # 新闻API源
        self.api_sources = {
            'newsapi': {
                'url': 'https://newsapi.org/v2/top-headlines',
                'params': {'country': 'cn', 'pageSize': 20},
                'parser': self._parse_newsapi
            }
        }
        
        # 免费新闻API
        self.free_sources = {
            'hacker_news': {
                'url': 'https://hacker-news.firebaseio.com/v0/topstories.json',
                'parser': self._parse_hacker_news
            },
            'reddit_programming': {
                'url': 'https://www.reddit.com/r/programming/hot.json',
                'parser': self._parse_reddit
            }
        }
    
    def _contains_keywords(self, text: str) -> bool:
        """检查文本是否包含关键词"""
        if not self.keywords:
            return True
        
        text_lower = text.lower()
        return any(keyword.lower() in text_lower for keyword in self.keywords)
    
    def _parse_newsapi(self, data: dict) -> List[Dict]:
        """解析NewsAPI数据"""
        news_list = []
        try:
            articles = data.get('articles', [])
            for article in articles:
                title = article.get('title', '')
                description = article.get('description', '')
                
                if self._contains_keywords(title + ' ' + description):
                    news_list.append({
                        'title': title,
                        'link': article.get('url', ''),
                        'source': article.get('source', {}).get('name', 'NewsAPI'),
                        'pub_date': article.get('publishedAt', ''),
                        'description': description,
                        'author': article.get('author', ''),
                        'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
        except Exception as e:
            logging.error(f"解析NewsAPI数据失败: {e}")
        
        return news_list
    
    def _parse_hacker_news(self, story_ids: List[int]) -> List[Dict]:
        """解析Hacker News数据"""
        news_list = []
        try:
            for story_id in story_ids[:15]:  # 取前15个
                story_url = f"https://hacker-news.firebaseio.com/v0/item/{story_id}.json"
                response = self.session.get(story_url, timeout=5)
                story_data = response.json()
                
                if story_data and story_data.get('title'):
                    title = story_data['title']
                    if self._contains_keywords(title):
                        news_list.append({
                            'title': f"HN: {title}",
                            'link': story_data.get('url', f"https://news.ycombinator.com/item?id={story_id}"),
                            'source': 'Hacker News',
                            'pub_date': datetime.fromtimestamp(story_data.get('time', 0)).strftime('%Y-%m-%d %H:%M:%S'),
                            'description': f"评分: {story_data.get('score', 0)}, 评论: {story_data.get('descendants', 0)}",
                            'author': story_data.get('by', ''),
                            'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
        except Exception as e:
            logging.error(f"解析Hacker News失败: {e}")
        
        return news_list
    
    def _parse_reddit(self, data: dict) -> List[Dict]:
        """解析Reddit数据"""
        news_list = []
        try:
            posts = data.get('data', {}).get('children', [])
            for post in posts[:15]:  # 取前15个
                post_data = post.get('data', {})
                title = post_data.get('title', '')
                
                if self._contains_keywords(title):
                    news_list.append({
                        'title': f"Reddit: {title}",
                        'link': f"https://reddit.com{post_data.get('permalink', '')}",
                        'source': 'Reddit Programming',
                        'pub_date': datetime.fromtimestamp(post_data.get('created_utc', 0)).strftime('%Y-%m-%d %H:%M:%S'),
                        'description': post_data.get('selftext', '')[:200] + '...' if post_data.get('selftext') else '',
                        'author': post_data.get('author', ''),
                        'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
        except Exception as e:
            logging.error(f"解析Reddit数据失败: {e}")
        
        return news_list
    
    def fetch_from_api(self, source_name: str, api_key: str = None) -> List[Dict]:
        """从API获取新闻"""
        if source_name not in self.api_sources:
            logging.error(f"不支持的API源: {source_name}")
            return []
        
        source_info = self.api_sources[source_name]
        params = source_info['params'].copy()
        
        if api_key:
            params['apiKey'] = api_key
        
        if self.keywords:
            params['q'] = ' OR '.join(self.keywords)
        
        try:
            response = self.session.get(source_info['url'], params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            return source_info['parser'](data)
        except Exception as e:
            logging.error(f"获取{source_name}数据失败: {e}")
            return []
    
    def fetch_from_free_sources(self) -> List[Dict]:
        """从免费源获取新闻"""
        all_news = []
        
        for source_name, source_info in self.free_sources.items():
            try:
                logging.info(f"正在获取 {source_name} 数据...")
                response = self.session.get(source_info['url'], timeout=10)
                response.raise_for_status()
                
                if source_name == 'hacker_news':
                    story_ids = response.json()
                    news_list = source_info['parser'](story_ids)
                else:
                    data = response.json()
                    news_list = source_info['parser'](data)
                
                all_news.extend(news_list)
                logging.info(f"从 {source_name} 获取到 {len(news_list)} 条新闻")
                
            except Exception as e:
                logging.error(f"获取 {source_name} 失败: {e}")
        
        return all_news
    
    def save_news(self, news_list: List[Dict], format_type: str = 'json', filename: str = None):
        """保存新闻"""
        if not news_list:
            logging.warning("没有新闻数据可保存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if format_type == 'json':
            filename = filename or f"news_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(news_list, f, ensure_ascii=False, indent=2)
        
        elif format_type == 'html':
            filename = filename or f"news_{timestamp}.html"
            self._save_as_html(news_list, filename)
        
        elif format_type == 'markdown':
            filename = filename or f"news_{timestamp}.md"
            self._save_as_markdown(news_list, filename)
        
        logging.info(f"新闻已保存到 {filename}")
    
    def _save_as_html(self, news_list: List[Dict], filename: str):
        """保存为HTML格式"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        news_count = len(news_list)

        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>热门新闻汇总</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .news-item {{ border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }}
        .title {{ font-size: 18px; font-weight: bold; color: #333; }}
        .meta {{ color: #666; font-size: 14px; margin: 5px 0; }}
        .description {{ margin: 10px 0; }}
        .link {{ color: #007bff; text-decoration: none; }}
    </style>
</head>
<body>
    <h1>热门新闻汇总</h1>
    <p>生成时间: {current_time}</p>
    <p>总计: {news_count} 条新闻</p>
"""
        
        for i, news in enumerate(news_list, 1):
            html_content += f"""
    <div class="news-item">
        <div class="title">{i}. {news['title']}</div>
        <div class="meta">来源: {news['source']} | 发布时间: {news.get('pub_date', '未知')} | 作者: {news.get('author', '未知')}</div>
        <div class="description">{news.get('description', '')}</div>
        <a href="{news['link']}" class="link" target="_blank">查看原文</a>
    </div>
"""
        
        html_content += """
</body>
</html>
"""
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def _save_as_markdown(self, news_list: List[Dict], filename: str):
        """保存为Markdown格式"""
        md_content = f"""# 热门新闻汇总

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
总计: {len(news_list)} 条新闻

---

"""
        
        for i, news in enumerate(news_list, 1):
            md_content += f"""## {i}. {news['title']}

**来源**: {news['source']}  
**发布时间**: {news.get('pub_date', '未知')}  
**作者**: {news.get('author', '未知')}  

{news.get('description', '')}

[查看原文]({news['link']})

---

"""
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(md_content)
    
    def filter_by_date(self, news_list: List[Dict], days: int = 1) -> List[Dict]:
        """按日期过滤新闻"""
        cutoff_date = datetime.now() - timedelta(days=days)
        filtered_news = []
        
        for news in news_list:
            try:
                pub_date_str = news.get('pub_date', '')
                if pub_date_str:
                    # 尝试解析不同的日期格式
                    for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%dT%H:%M:%SZ', '%Y-%m-%dT%H:%M:%S']:
                        try:
                            pub_date = datetime.strptime(pub_date_str[:19], fmt)
                            if pub_date >= cutoff_date:
                                filtered_news.append(news)
                            break
                        except ValueError:
                            continue
                else:
                    # 如果没有发布日期，保留新闻
                    filtered_news.append(news)
            except Exception:
                # 如果日期解析失败，保留新闻
                filtered_news.append(news)
        
        return filtered_news
    
    def run_scheduled_crawl(self, interval_hours: int = 6):
        """定时爬取新闻"""
        def crawl_job():
            logging.info("开始定时爬取新闻...")
            news_list = self.fetch_from_free_sources()
            if news_list:
                self.save_news(news_list, 'json')
                self.save_news(news_list, 'html')
                logging.info(f"定时爬取完成，获取到 {len(news_list)} 条新闻")
            else:
                logging.warning("定时爬取未获取到新闻")
        
        schedule.every(interval_hours).hours.do(crawl_job)
        
        logging.info(f"已设置定时爬取，每 {interval_hours} 小时执行一次")
        logging.info("按 Ctrl+C 停止定时爬取")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            logging.info("定时爬取已停止")

def main():
    parser = argparse.ArgumentParser(description='高级新闻爬虫')
    parser.add_argument('--keywords', nargs='+', help='关键词过滤')
    parser.add_argument('--format', choices=['json', 'html', 'markdown'], default='json', help='输出格式')
    parser.add_argument('--days', type=int, default=7, help='过滤最近几天的新闻')
    parser.add_argument('--schedule', type=int, help='定时爬取间隔（小时）')
    parser.add_argument('--api-key', help='NewsAPI密钥')
    
    args = parser.parse_args()
    
    # 创建爬虫实例
    crawler = AdvancedNewsCrawler(keywords=args.keywords)
    
    if args.schedule:
        # 定时爬取模式
        crawler.run_scheduled_crawl(args.schedule)
    else:
        # 单次爬取模式
        print("高级新闻爬虫")
        if args.keywords:
            print(f"关键词过滤: {', '.join(args.keywords)}")
        
        # 获取新闻
        news_list = crawler.fetch_from_free_sources()
        
        # 如果有API密钥，也尝试从NewsAPI获取
        if args.api_key:
            api_news = crawler.fetch_from_api('newsapi', args.api_key)
            news_list.extend(api_news)
        
        # 按日期过滤
        if args.days:
            news_list = crawler.filter_by_date(news_list, args.days)
        
        if news_list:
            print(f"\n获取到 {len(news_list)} 条新闻")
            
            # 显示前10条
            for i, news in enumerate(news_list[:10], 1):
                print(f"\n{i}. 【{news['source']}】{news['title']}")
                print(f"   {news['link']}")
            
            if len(news_list) > 10:
                print(f"\n... 还有 {len(news_list) - 10} 条新闻")
            
            # 保存新闻
            crawler.save_news(news_list, args.format)
        else:
            print("未获取到符合条件的新闻")

if __name__ == "__main__":
    main()
