<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蚂蚁庄园 - 养小鸡</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body>
    <!-- 游戏主容器 -->
    <div class="game-container">
        <!-- 顶部状态栏 -->
        <div class="status-bar">
            <div class="status-item">
                <span class="icon">🌾</span>
                <span class="label">饲料:</span>
                <span class="value" id="feedCount">100</span>
            </div>
            <div class="status-item">
                <span class="icon">🥚</span>
                <span class="label">鸡蛋:</span>
                <span class="value" id="eggCount">0</span>
            </div>
            <div class="status-item">
                <span class="icon">⭐</span>
                <span class="label">等级:</span>
                <span class="value" id="chickenLevel">1</span>
            </div>
        </div>

        <!-- 农场主区域 -->
        <div class="farm-area">
            <!-- 天空背景 -->
            <div class="sky">
                <div class="cloud cloud1"></div>
                <div class="cloud cloud2"></div>
                <div class="sun"></div>
            </div>

            <!-- 小鸡区域 -->
            <div class="chicken-area">
                <div class="chicken" id="chicken">
                    <div class="chicken-body">
                        <div class="chicken-head">
                            <div class="chicken-eye left-eye"></div>
                            <div class="chicken-eye right-eye"></div>
                            <div class="chicken-beak"></div>
                        </div>
                        <div class="chicken-wing"></div>
                        <div class="chicken-tail"></div>
                    </div>
                    <div class="chicken-legs">
                        <div class="chicken-leg left-leg"></div>
                        <div class="chicken-leg right-leg"></div>
                    </div>
                </div>

                <!-- 鸡蛋显示区域 -->
                <div class="eggs-container" id="eggsContainer">
                    <!-- 鸡蛋会动态生成在这里 -->
                </div>

                <!-- 饲料掉落效果区域 -->
                <div class="feed-effects" id="feedEffects">
                    <!-- 饲料动画会在这里显示 -->
                </div>
            </div>

            <!-- 草地背景 -->
            <div class="grass"></div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <button class="action-btn feed-btn" id="feedBtn">
                <span class="btn-icon">🌾</span>
                <span class="btn-text">喂食</span>
            </button>
            
            <button class="action-btn collect-btn" id="collectBtn">
                <span class="btn-icon">🥚</span>
                <span class="btn-text">收蛋</span>
            </button>

            <button class="action-btn info-btn" id="infoBtn">
                <span class="btn-icon">ℹ️</span>
                <span class="btn-text">信息</span>
            </button>
        </div>

        <!-- 信息面板 -->
        <div class="info-panel" id="infoPanel">
            <div class="info-content">
                <h3>小鸡状态</h3>
                <div class="info-item">
                    <span>饥饿度:</span>
                    <div class="progress-bar">
                        <div class="progress-fill" id="hungerBar"></div>
                    </div>
                </div>
                <div class="info-item">
                    <span>开心度:</span>
                    <div class="progress-bar">
                        <div class="progress-fill" id="happinessBar"></div>
                    </div>
                </div>
                <div class="info-item">
                    <span>经验值:</span>
                    <div class="progress-bar">
                        <div class="progress-fill" id="expBar"></div>
                    </div>
                </div>
                <button class="close-btn" id="closeInfoBtn">关闭</button>
            </div>
        </div>

        <!-- 消息提示 -->
        <div class="message-toast" id="messageToast">
            <span id="messageText"></span>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/storage.js"></script>
    <script src="js/chicken.js"></script>
    <script src="js/game.js"></script>
</body>
</html>
