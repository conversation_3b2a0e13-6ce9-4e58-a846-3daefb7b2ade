<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天气预报</title>
<style>
    body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
        background-color: #f3f3f3;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
    }
    .container {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        width: 90%;
        max-width: 400px;
        padding: 20px;
        text-align: center;
        animation: fadeIn 1s ease-in-out;
    }
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    h1 {
        color: #333;
        margin-bottom: 10px;
    }
    .weather-icon {
        width: 100px;
        height: 100px;
        margin: 20px 0;
        animation: rotate 5s linear infinite;
    }
    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    .temperature {
        font-size: 2em;
        color: #007aff;
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    .description {
        color: #555;
        margin-top: 10px;
    }
    .details {
        margin-top: 20px;
    }
    .humidity, .wind-speed {
        margin: 5px 0;
    }
</style>
</head>
<body>
<div class="container">
    <h1>天气预报</h1>
    <img src="https://openweathermap.org/img/wn/<EMAIL>" alt="晴天" class="weather-icon">
    <div class="temperature">25°C</div>
    <div class="description">晴天</div>
    <div class="details">
        <div class="humidity">湿度: 60%</div>
        <div class="wind-speed">风速: 10 km/h</div>
    </div>
</div>
</body>
</html>
</write_to_file>
