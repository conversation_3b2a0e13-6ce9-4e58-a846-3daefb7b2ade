// 游戏主系统管理类
class GameSystem {
    constructor() {
        this.gameData = {
            feedCount: 100,
            eggCount: 0,
            totalEggs: 0,
            lastFeedRegenTime: Date.now(),
            gameStartTime: Date.now()
        };
        
        this.chickenSystem = null;
        this.storageSystem = null;
        this.eggs = [];
        this.feedRegenInterval = null;
        
        this.init();
    }
    
    init() {
        // 初始化存储系统
        this.storageSystem = new StorageSystem();
        
        // 加载游戏数据
        this.loadGameData();
        
        // 初始化小鸡系统
        this.chickenSystem = new ChickenSystem();

        // 加载小鸡数据
        const saveData = this.storageSystem.loadGame();
        if (saveData && saveData.chickenData) {
            this.chickenSystem.setStatus(saveData.chickenData);
        }

        // 设置全局引用
        window.gameSystem = this;
        
        // 绑定事件
        this.bindEvents();
        
        // 开始饲料恢复
        this.startFeedRegeneration();
        
        // 更新显示
        this.updateDisplay();
        
        console.log('蚂蚁庄园游戏初始化完成！');
    }
    
    // 绑定事件
    bindEvents() {
        // 喂食按钮
        const feedBtn = document.getElementById('feedBtn');
        if (feedBtn) {
            feedBtn.addEventListener('click', () => this.feedChicken());
        }
        
        // 收蛋按钮
        const collectBtn = document.getElementById('collectBtn');
        if (collectBtn) {
            collectBtn.addEventListener('click', () => this.collectAllEggs());
        }
        
        // 信息按钮
        const infoBtn = document.getElementById('infoBtn');
        if (infoBtn) {
            infoBtn.addEventListener('click', () => this.toggleInfoPanel());
        }
        
        // 关闭信息面板
        const closeInfoBtn = document.getElementById('closeInfoBtn');
        if (closeInfoBtn) {
            closeInfoBtn.addEventListener('click', () => this.hideInfoPanel());
        }
        
        // 点击小鸡
        const chicken = document.getElementById('chicken');
        if (chicken) {
            chicken.addEventListener('click', () => this.chickenSystem.onChickenClick());
        }
        
        // 点击信息面板背景关闭
        const infoPanel = document.getElementById('infoPanel');
        if (infoPanel) {
            infoPanel.addEventListener('click', (e) => {
                if (e.target === infoPanel) {
                    this.hideInfoPanel();
                }
            });
        }
        
        // 页面关闭前保存数据
        window.addEventListener('beforeunload', () => {
            this.saveGameData();
        });
        
        // 定期自动保存
        setInterval(() => {
            this.saveGameData();
        }, 30000); // 每30秒自动保存
    }
    
    // 喂食小鸡
    feedChicken() {
        if (this.gameData.feedCount <= 0) {
            this.showMessage('饲料不足！请等待饲料恢复。');
            return;
        }
        
        const result = this.chickenSystem.feed();
        
        if (result.success) {
            this.gameData.feedCount--;
            this.createFeedEffect();
            this.updateDisplay();
            this.showMessage(result.message);
            this.saveGameData();
        } else {
            this.showMessage(result.message);
        }
    }
    
    // 创建饲料掉落效果
    createFeedEffect() {
        const feedEffects = document.getElementById('feedEffects');
        if (!feedEffects) return;

        // 限制同时存在的粒子数量
        const existingParticles = feedEffects.querySelectorAll('.feed-particle');
        if (existingParticles.length > 10) {
            return; // 避免过多粒子影响性能
        }

        // 创建多个饲料颗粒
        for (let i = 0; i < 5; i++) {
            setTimeout(() => {
                const particle = document.createElement('div');
                particle.className = 'feed-particle';

                // 随机位置
                const x = 50 + (Math.random() - 0.5) * 60; // 小鸡周围
                const y = 20;

                particle.style.left = `${x}%`;
                particle.style.top = `${y}%`;

                feedEffects.appendChild(particle);

                // 动画结束后移除
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 1500);
            }, i * 100);
        }
    }
    
    // 添加鸡蛋
    addEgg() {
        const eggsContainer = document.getElementById('eggsContainer');
        if (!eggsContainer) return;
        
        const egg = document.createElement('div');
        egg.className = 'egg';
        
        // 随机位置（小鸡附近）
        const x = 40 + Math.random() * 20;
        const y = 70 + Math.random() * 20;
        
        egg.style.left = `${x}%`;
        egg.style.bottom = `${y}px`;
        
        // 点击收集事件
        egg.addEventListener('click', () => this.collectEgg(egg));
        
        eggsContainer.appendChild(egg);
        this.eggs.push(egg);
        
        this.showMessage('小鸡生了一个蛋！');
    }
    
    // 收集单个鸡蛋
    collectEgg(eggElement) {
        if (!eggElement || eggElement.classList.contains('collecting')) return;
        
        eggElement.classList.add('collecting');
        
        // 增加鸡蛋数量
        this.gameData.eggCount++;
        this.gameData.totalEggs++;
        
        // 显示数值变化
        this.showValueChange(eggElement, '+1', 'positive');
        
        // 移除鸡蛋元素
        setTimeout(() => {
            if (eggElement.parentNode) {
                eggElement.parentNode.removeChild(eggElement);
            }
            
            // 从数组中移除
            const index = this.eggs.indexOf(eggElement);
            if (index > -1) {
                this.eggs.splice(index, 1);
            }
        }, 600);
        
        this.updateDisplay();
        this.saveGameData();
    }
    
    // 收集所有鸡蛋
    collectAllEggs() {
        if (this.eggs.length === 0) {
            this.showMessage('没有鸡蛋可以收集！');
            return;
        }
        
        const eggCount = this.eggs.length;
        
        // 收集所有鸡蛋
        this.eggs.forEach((egg, index) => {
            setTimeout(() => {
                this.collectEgg(egg);
            }, index * 100);
        });
        
        this.showMessage(`收集了 ${eggCount} 个鸡蛋！`);
    }
    
    // 开始饲料恢复
    startFeedRegeneration() {
        this.feedRegenInterval = setInterval(() => {
            const now = Date.now();
            const timeSinceLastRegen = now - this.gameData.lastFeedRegenTime;
            
            // 每分钟恢复1个饲料，最多100个
            if (timeSinceLastRegen >= 60000 && this.gameData.feedCount < 100) {
                this.gameData.feedCount = Math.min(100, this.gameData.feedCount + 1);
                this.gameData.lastFeedRegenTime = now;
                this.updateDisplay();
                this.saveGameData();
            }
        }, 10000); // 每10秒检查一次
    }
    
    // 显示消息提示
    showMessage(message) {
        const messageToast = document.getElementById('messageToast');
        const messageText = document.getElementById('messageText');
        
        if (!messageToast || !messageText) return;
        
        messageText.textContent = message;
        messageToast.classList.add('show');
        
        setTimeout(() => {
            messageToast.classList.remove('show');
        }, 2000);
    }
    
    // 显示数值变化
    showValueChange(element, text, type = 'positive') {
        const rect = element.getBoundingClientRect();
        const change = document.createElement('div');
        change.className = `value-change ${type}`;
        change.textContent = text;
        
        change.style.left = `${rect.left + rect.width / 2}px`;
        change.style.top = `${rect.top}px`;
        change.style.position = 'fixed';
        
        document.body.appendChild(change);
        
        setTimeout(() => {
            if (change.parentNode) {
                change.parentNode.removeChild(change);
            }
        }, 1500);
    }
    
    // 切换信息面板
    toggleInfoPanel() {
        const infoPanel = document.getElementById('infoPanel');
        if (!infoPanel) return;
        
        if (infoPanel.style.display === 'flex') {
            this.hideInfoPanel();
        } else {
            this.showInfoPanel();
        }
    }
    
    // 显示信息面板
    showInfoPanel() {
        const infoPanel = document.getElementById('infoPanel');
        if (!infoPanel) return;
        
        infoPanel.style.display = 'flex';
        infoPanel.classList.add('show');
        
        // 更新小鸡状态显示
        if (this.chickenSystem) {
            this.chickenSystem.updateDisplay();
        }
    }
    
    // 隐藏信息面板
    hideInfoPanel() {
        const infoPanel = document.getElementById('infoPanel');
        if (!infoPanel) return;
        
        infoPanel.classList.remove('show');
        setTimeout(() => {
            infoPanel.style.display = 'none';
        }, 300);
    }
    
    // 更新显示
    updateDisplay() {
        // 更新饲料数量
        const feedCountElement = document.getElementById('feedCount');
        if (feedCountElement) {
            feedCountElement.textContent = this.gameData.feedCount;
        }
        
        // 更新鸡蛋数量
        const eggCountElement = document.getElementById('eggCount');
        if (eggCountElement) {
            eggCountElement.textContent = this.gameData.eggCount;
        }
    }
    
    // 保存游戏数据
    saveGameData() {
        if (!this.storageSystem) return;
        
        const saveData = {
            gameData: this.gameData,
            chickenData: this.chickenSystem ? this.chickenSystem.getStatus() : null
        };
        
        this.storageSystem.saveGame(saveData);
    }
    
    // 加载游戏数据
    loadGameData() {
        if (!this.storageSystem) return;
        
        const saveData = this.storageSystem.loadGame();
        
        if (saveData) {
            if (saveData.gameData) {
                this.gameData = { ...this.gameData, ...saveData.gameData };
            }
            
            // 小鸡数据会在小鸡系统初始化后加载
            if (saveData.chickenData && this.chickenSystem) {
                this.chickenSystem.setStatus(saveData.chickenData);
            }
        }
    }
    
    // 重置游戏
    resetGame() {
        if (confirm('确定要重置游戏吗？所有进度将会丢失！')) {
            this.storageSystem.clearGame();
            location.reload();
        }
    }
    
    // 销毁游戏
    destroy() {
        if (this.feedRegenInterval) {
            clearInterval(this.feedRegenInterval);
        }
        
        if (this.chickenSystem) {
            this.chickenSystem.destroy();
        }
        
        this.saveGameData();
    }
}

// 页面加载完成后初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    try {
        // 显示加载动画
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'loading';
        loadingDiv.innerHTML = '<div class="loading-spinner"></div>';
        document.body.appendChild(loadingDiv);

        // 延迟初始化以显示加载动画
        setTimeout(() => {
            try {
                new GameSystem();

                // 隐藏加载动画
                setTimeout(() => {
                    if (loadingDiv.parentNode) {
                        loadingDiv.parentNode.removeChild(loadingDiv);
                    }
                }, 500);
            } catch (error) {
                console.error('游戏初始化失败:', error);
                alert('游戏初始化失败，请刷新页面重试');

                if (loadingDiv.parentNode) {
                    loadingDiv.parentNode.removeChild(loadingDiv);
                }
            }
        }, 100);
    } catch (error) {
        console.error('页面加载失败:', error);
        alert('页面加载失败，请刷新页面重试');
    }
});

// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('全局错误:', event.error);
});

// 处理未捕获的Promise错误
window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise错误:', event.reason);
    event.preventDefault();
});
