#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版新闻爬虫
使用RSS源和公开API获取新闻
"""

import requests
import json
import xml.etree.ElementTree as ET
from datetime import datetime
import csv
import logging
from typing import List, Dict

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SimpleNewsCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # RSS新闻源
        self.rss_sources = {
            'people': 'http://www.people.com.cn/rss/politics.xml',
            'xinhua': 'http://www.xinhuanet.com/politics/news_politics.xml',
            'china': 'http://www.china.com.cn/rss/politics.xml'
        }
    
    def fetch_rss_news(self, source_name: str, rss_url: str) -> List[Dict]:
        """从RSS源获取新闻"""
        news_list = []
        try:
            logging.info(f"正在获取 {source_name} RSS新闻...")
            response = self.session.get(rss_url, timeout=10)
            response.encoding = 'utf-8'
            
            # 解析XML
            root = ET.fromstring(response.content)
            
            # 查找所有item
            items = root.findall('.//item')
            
            for item in items[:15]:  # 取前15条
                title_elem = item.find('title')
                link_elem = item.find('link')
                pub_date_elem = item.find('pubDate')
                description_elem = item.find('description')
                
                if title_elem is not None and link_elem is not None:
                    title = title_elem.text.strip() if title_elem.text else ""
                    link = link_elem.text.strip() if link_elem.text else ""
                    pub_date = pub_date_elem.text.strip() if pub_date_elem and pub_date_elem.text else ""
                    description = description_elem.text.strip() if description_elem and description_elem.text else ""
                    
                    if title and link:
                        news_list.append({
                            'title': title,
                            'link': link,
                            'source': source_name,
                            'pub_date': pub_date,
                            'description': description[:200] + '...' if len(description) > 200 else description,
                            'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
            
            logging.info(f"从 {source_name} 获取到 {len(news_list)} 条新闻")
            
        except Exception as e:
            logging.error(f"获取 {source_name} RSS新闻失败: {e}")
        
        return news_list
    
    def fetch_github_trending(self) -> List[Dict]:
        """获取GitHub热门项目（作为技术新闻）"""
        news_list = []
        try:
            logging.info("正在获取GitHub热门项目...")
            # 使用GitHub API获取热门仓库
            url = "https://api.github.com/search/repositories"
            params = {
                'q': 'created:>2024-01-01',
                'sort': 'stars',
                'order': 'desc',
                'per_page': 10
            }
            
            response = self.session.get(url, params=params, timeout=10)
            data = response.json()
            
            for repo in data.get('items', []):
                news_list.append({
                    'title': f"GitHub热门: {repo['name']} - {repo['description'][:100] if repo['description'] else ''}",
                    'link': repo['html_url'],
                    'source': 'GitHub',
                    'pub_date': repo['created_at'],
                    'description': repo['description'] or '',
                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            logging.info(f"获取到 {len(news_list)} 个GitHub热门项目")
            
        except Exception as e:
            logging.error(f"获取GitHub热门项目失败: {e}")
        
        return news_list
    
    def fetch_hacker_news(self) -> List[Dict]:
        """获取Hacker News热门文章"""
        news_list = []
        try:
            logging.info("正在获取Hacker News热门文章...")
            
            # 获取热门文章ID
            top_stories_url = "https://hacker-news.firebaseio.com/v0/topstories.json"
            response = self.session.get(top_stories_url, timeout=10)
            story_ids = response.json()[:10]  # 取前10个
            
            for story_id in story_ids:
                story_url = f"https://hacker-news.firebaseio.com/v0/item/{story_id}.json"
                story_response = self.session.get(story_url, timeout=5)
                story_data = story_response.json()
                
                if story_data and story_data.get('title'):
                    news_list.append({
                        'title': f"HN热门: {story_data['title']}",
                        'link': story_data.get('url', f"https://news.ycombinator.com/item?id={story_id}"),
                        'source': 'Hacker News',
                        'pub_date': datetime.fromtimestamp(story_data.get('time', 0)).strftime('%Y-%m-%d %H:%M:%S'),
                        'description': f"评分: {story_data.get('score', 0)}, 评论: {story_data.get('descendants', 0)}",
                        'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
            
            logging.info(f"获取到 {len(news_list)} 条Hacker News文章")
            
        except Exception as e:
            logging.error(f"获取Hacker News失败: {e}")
        
        return news_list
    
    def crawl_all_news(self) -> List[Dict]:
        """爬取所有新闻源"""
        all_news = []
        
        # 爬取RSS新闻
        for source_name, rss_url in self.rss_sources.items():
            try:
                news_list = self.fetch_rss_news(source_name, rss_url)
                all_news.extend(news_list)
            except Exception as e:
                logging.error(f"爬取 {source_name} 失败: {e}")
        
        # 爬取GitHub热门
        try:
            github_news = self.fetch_github_trending()
            all_news.extend(github_news)
        except Exception as e:
            logging.error(f"爬取GitHub热门失败: {e}")
        
        # 爬取Hacker News
        try:
            hn_news = self.fetch_hacker_news()
            all_news.extend(hn_news)
        except Exception as e:
            logging.error(f"爬取Hacker News失败: {e}")
        
        return all_news
    
    def save_to_json(self, news_list: List[Dict], filename: str = None):
        """保存到JSON文件"""
        if not filename:
            filename = f"news_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(news_list, f, ensure_ascii=False, indent=2)
        logging.info(f"新闻已保存到 {filename}")
    
    def save_to_csv(self, news_list: List[Dict], filename: str = None):
        """保存到CSV文件"""
        if not filename:
            filename = f"news_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        if news_list:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=news_list[0].keys())
                writer.writeheader()
                writer.writerows(news_list)
        logging.info(f"新闻已保存到 {filename}")
    
    def print_news(self, news_list: List[Dict], limit: int = 20):
        """打印新闻"""
        print(f"\n{'='*100}")
        print(f"热门新闻汇总 (共{len(news_list)}条，显示前{min(limit, len(news_list))}条)")
        print(f"{'='*100}")
        
        for i, news in enumerate(news_list[:limit], 1):
            print(f"\n{i}. 【{news['source']}】{news['title']}")
            print(f"   链接: {news['link']}")
            if news.get('description'):
                print(f"   描述: {news['description']}")
            print(f"   发布时间: {news.get('pub_date', '未知')}")
            print(f"   爬取时间: {news['crawl_time']}")

def main():
    """主函数"""
    crawler = SimpleNewsCrawler()
    
    print("简化版新闻爬虫")
    print("正在爬取各大新闻源...")
    
    # 爬取所有新闻
    news_list = crawler.crawl_all_news()
    
    if news_list:
        # 显示新闻
        crawler.print_news(news_list)
        
        # 保存选项
        print(f"\n总共爬取到 {len(news_list)} 条新闻")
        save_choice = input("是否保存新闻? (y/n): ").strip().lower()
        
        if save_choice == 'y':
            format_choice = input("选择保存格式 (json/csv/both): ").strip().lower()
            
            if format_choice == 'json':
                crawler.save_to_json(news_list)
            elif format_choice == 'csv':
                crawler.save_to_csv(news_list)
            elif format_choice == 'both':
                crawler.save_to_json(news_list)
                crawler.save_to_csv(news_list)
            else:
                print("无效格式，默认保存为JSON")
                crawler.save_to_json(news_list)
    else:
        print("未获取到任何新闻数据")

if __name__ == "__main__":
    main()
