#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热门新闻爬虫程序
支持爬取多个新闻源的热门新闻
"""

import requests
from bs4 import BeautifulSoup
import json
import time
from datetime import datetime
import csv
import os
from typing import List, Dict, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('news_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class NewsCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.news_sources = {
            'sina': {
                'url': 'https://news.sina.com.cn/',
                'parser': self._parse_sina_news
            },
            'netease': {
                'url': 'https://news.163.com/',
                'parser': self._parse_netease_news
            },
            'tencent': {
                'url': 'https://news.qq.com/',
                'parser': self._parse_tencent_news
            }
        }
        
    def _get_page_content(self, url: str) -> Optional[BeautifulSoup]:
        """获取网页内容"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return BeautifulSoup(response.text, 'html.parser')
        except Exception as e:
            logging.error(f"获取页面内容失败 {url}: {e}")
            return None
    
    def _parse_sina_news(self, soup: BeautifulSoup) -> List[Dict]:
        """解析新浪新闻"""
        news_list = []
        try:
            # 查找新闻链接
            news_items = soup.find_all('a', href=True)
            for item in news_items[:20]:  # 取前20条
                title = item.get_text(strip=True)
                link = item.get('href')
                
                if title and len(title) > 10 and 'http' in link:
                    news_list.append({
                        'title': title,
                        'link': link,
                        'source': '新浪新闻',
                        'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
        except Exception as e:
            logging.error(f"解析新浪新闻失败: {e}")
        
        return news_list
    
    def _parse_netease_news(self, soup: BeautifulSoup) -> List[Dict]:
        """解析网易新闻"""
        news_list = []
        try:
            # 查找新闻链接
            news_items = soup.find_all('a', href=True)
            for item in news_items[:20]:  # 取前20条
                title = item.get_text(strip=True)
                link = item.get('href')
                
                if title and len(title) > 10 and ('163.com' in link or link.startswith('http')):
                    if not link.startswith('http'):
                        link = 'https://news.163.com' + link
                    
                    news_list.append({
                        'title': title,
                        'link': link,
                        'source': '网易新闻',
                        'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
        except Exception as e:
            logging.error(f"解析网易新闻失败: {e}")
        
        return news_list
    
    def _parse_tencent_news(self, soup: BeautifulSoup) -> List[Dict]:
        """解析腾讯新闻"""
        news_list = []
        try:
            # 查找新闻链接
            news_items = soup.find_all('a', href=True)
            for item in news_items[:20]:  # 取前20条
                title = item.get_text(strip=True)
                link = item.get('href')
                
                if title and len(title) > 10 and ('qq.com' in link or link.startswith('http')):
                    if not link.startswith('http'):
                        link = 'https://news.qq.com' + link
                    
                    news_list.append({
                        'title': title,
                        'link': link,
                        'source': '腾讯新闻',
                        'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
        except Exception as e:
            logging.error(f"解析腾讯新闻失败: {e}")
        
        return news_list
    
    def crawl_news_from_source(self, source_name: str) -> List[Dict]:
        """从指定新闻源爬取新闻"""
        if source_name not in self.news_sources:
            logging.error(f"不支持的新闻源: {source_name}")
            return []
        
        source_info = self.news_sources[source_name]
        logging.info(f"开始爬取 {source_name} 新闻...")
        
        soup = self._get_page_content(source_info['url'])
        if not soup:
            return []
        
        news_list = source_info['parser'](soup)
        logging.info(f"从 {source_name} 爬取到 {len(news_list)} 条新闻")
        
        return news_list
    
    def crawl_all_news(self) -> List[Dict]:
        """爬取所有新闻源的新闻"""
        all_news = []
        
        for source_name in self.news_sources.keys():
            news_list = self.crawl_news_from_source(source_name)
            all_news.extend(news_list)
            time.sleep(2)  # 避免请求过于频繁
        
        return all_news
    
    def save_to_json(self, news_list: List[Dict], filename: str = None):
        """保存新闻到JSON文件"""
        if not filename:
            filename = f"news_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(news_list, f, ensure_ascii=False, indent=2)
            logging.info(f"新闻已保存到 {filename}")
        except Exception as e:
            logging.error(f"保存JSON文件失败: {e}")
    
    def save_to_csv(self, news_list: List[Dict], filename: str = None):
        """保存新闻到CSV文件"""
        if not filename:
            filename = f"news_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                if news_list:
                    writer = csv.DictWriter(f, fieldnames=news_list[0].keys())
                    writer.writeheader()
                    writer.writerows(news_list)
            logging.info(f"新闻已保存到 {filename}")
        except Exception as e:
            logging.error(f"保存CSV文件失败: {e}")
    
    def print_news(self, news_list: List[Dict], limit: int = 10):
        """打印新闻列表"""
        print(f"\n{'='*80}")
        print(f"热门新闻 (显示前{min(limit, len(news_list))}条)")
        print(f"{'='*80}")
        
        for i, news in enumerate(news_list[:limit], 1):
            print(f"\n{i}. 【{news['source']}】{news['title']}")
            print(f"   链接: {news['link']}")
            print(f"   时间: {news['time']}")

def main():
    """主函数"""
    crawler = NewsCrawler()
    
    print("热门新闻爬虫程序")
    print("1. 爬取所有新闻源")
    print("2. 爬取新浪新闻")
    print("3. 爬取网易新闻")
    print("4. 爬取腾讯新闻")
    
    choice = input("\n请选择操作 (1-4): ").strip()
    
    if choice == '1':
        news_list = crawler.crawl_all_news()
    elif choice == '2':
        news_list = crawler.crawl_news_from_source('sina')
    elif choice == '3':
        news_list = crawler.crawl_news_from_source('netease')
    elif choice == '4':
        news_list = crawler.crawl_news_from_source('tencent')
    else:
        print("无效选择")
        return
    
    if news_list:
        # 显示新闻
        crawler.print_news(news_list)
        
        # 询问是否保存
        save_choice = input("\n是否保存新闻? (y/n): ").strip().lower()
        if save_choice == 'y':
            format_choice = input("选择保存格式 (json/csv): ").strip().lower()
            if format_choice == 'json':
                crawler.save_to_json(news_list)
            elif format_choice == 'csv':
                crawler.save_to_csv(news_list)
            else:
                print("无效格式，默认保存为JSON")
                crawler.save_to_json(news_list)
    else:
        print("未获取到新闻数据")

if __name__ == "__main__":
    main()
