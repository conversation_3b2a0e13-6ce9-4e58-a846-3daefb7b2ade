/* 动画效果样式文件 */

/* 云朵飘动动画 */
@keyframes float {
    0% {
        transform: translateX(-100px);
    }
    100% {
        transform: translateX(calc(100vw + 100px));
    }
}

/* 小鸡基础动画 */
@keyframes chickenIdle {
    0%, 100% {
        transform: translateX(-50%) translateY(0px);
    }
    25% {
        transform: translateX(-50%) translateY(-2px);
    }
    50% {
        transform: translateX(-50%) translateY(0px);
    }
    75% {
        transform: translateX(-50%) translateY(-1px);
    }
}

/* 小鸡眨眼动画 */
@keyframes blink {
    0%, 90%, 100% {
        transform: scaleY(1);
    }
    95% {
        transform: scaleY(0.1);
    }
}

/* 小鸡吃食动画 */
@keyframes eating {
    0%, 100% {
        transform: translateX(-50%) translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateX(-50%) translateY(5px) rotate(-5deg);
    }
    50% {
        transform: translateX(-50%) translateY(8px) rotate(0deg);
    }
    75% {
        transform: translateX(-50%) translateY(5px) rotate(5deg);
    }
}

/* 小鸡开心跳跃动画 */
@keyframes happy {
    0%, 100% {
        transform: translateX(-50%) translateY(0px) scale(1);
    }
    25% {
        transform: translateX(-50%) translateY(-15px) scale(1.05);
    }
    50% {
        transform: translateX(-50%) translateY(-20px) scale(1.1);
    }
    75% {
        transform: translateX(-50%) translateY(-10px) scale(1.05);
    }
}

/* 小鸡生蛋动画 */
@keyframes layEgg {
    0%, 100% {
        transform: translateX(-50%) translateY(0px);
    }
    20% {
        transform: translateX(-50%) translateY(-5px);
    }
    40% {
        transform: translateX(-50%) translateY(0px);
    }
    60% {
        transform: translateX(-50%) translateY(-3px);
    }
    80% {
        transform: translateX(-50%) translateY(0px);
    }
}

/* 饲料掉落动画 */
@keyframes feedDrop {
    0% {
        opacity: 1;
        transform: translateY(-50px) scale(0.5);
    }
    50% {
        opacity: 1;
        transform: translateY(20px) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(50px) scale(0.8);
    }
}

/* 鸡蛋出现动画 */
@keyframes eggAppear {
    0% {
        opacity: 0;
        transform: scale(0) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.2) rotate(180deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(360deg);
    }
}

/* 鸡蛋收集动画 */
@keyframes eggCollect {
    0% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.5) translateY(-30px);
    }
    100% {
        opacity: 0;
        transform: scale(0.5) translateY(-60px);
    }
}

/* 按钮点击波纹效果 */
@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* 数值增加动画 */
@keyframes countUp {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    50% {
        transform: translateY(-10px) scale(1.2);
        opacity: 1;
    }
    100% {
        transform: translateY(-20px) scale(1);
        opacity: 0;
    }
}

/* 进度条填充动画 */
@keyframes progressFill {
    0% {
        width: 0%;
    }
    100% {
        width: var(--target-width);
    }
}

/* 太阳光芒动画 */
@keyframes sunGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
    }
}

/* 草地摆动动画 */
@keyframes grassSway {
    0%, 100% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(1deg);
    }
    75% {
        transform: rotate(-1deg);
    }
}

/* 小鸡翅膀扇动动画 */
@keyframes wingFlap {
    0%, 100% {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(-15deg);
    }
}

/* 应用动画的类 */
.chicken {
    animation: chickenIdle 4s ease-in-out infinite;
}

.chicken.eating {
    animation: eating 1s ease-in-out;
}

.chicken.happy {
    animation: happy 1.5s ease-in-out;
}

.chicken.laying {
    animation: layEgg 2s ease-in-out;
}

.chicken-eye {
    animation: blink 3s ease-in-out infinite;
}

.chicken-wing {
    animation: wingFlap 2s ease-in-out infinite;
}

.sun {
    animation: sunGlow 3s ease-in-out infinite;
}

.feed-particle {
    animation: feedDrop 1.5s ease-out forwards;
}

.egg {
    animation: eggAppear 0.8s ease-out;
}

.egg.collecting {
    animation: eggCollect 0.6s ease-out forwards;
}

/* 按钮波纹效果 */
.action-btn {
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.action-btn:active::before {
    width: 300px;
    height: 300px;
}

/* 数值变化提示 */
.value-change {
    position: absolute;
    font-weight: bold;
    font-size: 18px;
    color: #4CAF50;
    animation: countUp 1.5s ease-out forwards;
    pointer-events: none;
    z-index: 1000;
}

.value-change.negative {
    color: #f44336;
}

/* 特殊状态动画 */
.chicken.hungry .chicken-body {
    animation: shake 0.5s ease-in-out infinite;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

.chicken.sleepy {
    opacity: 0.7;
    animation: chickenIdle 6s ease-in-out infinite;
}

.chicken.excited .chicken-body {
    animation: bounce 0.8s ease-in-out infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* 页面加载动画 */
.game-container {
    animation: fadeIn 1s ease-out;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 信息面板动画 */
.info-panel.show {
    display: flex;
    animation: modalShow 0.3s ease-out;
}

@keyframes modalShow {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.info-content {
    animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
    0% {
        transform: translateY(50px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}
