// 本地存储管理系统
class StorageSystem {
    constructor() {
        this.storageKey = 'antFarmGame';
        this.version = '1.0.0';
        this.init();
    }
    
    init() {
        // 检查浏览器是否支持localStorage
        if (!this.isLocalStorageSupported()) {
            console.warn('浏览器不支持localStorage，游戏进度将无法保存');
            return;
        }
        
        // 检查并升级存档版本
        this.checkAndUpgradeData();
    }
    
    // 检查localStorage支持
    isLocalStorageSupported() {
        try {
            const test = 'test';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }
    
    // 保存游戏数据
    saveGame(gameData) {
        if (!this.isLocalStorageSupported()) {
            return false;
        }
        
        try {
            const saveData = {
                version: this.version,
                timestamp: Date.now(),
                data: gameData
            };
            
            const jsonString = JSON.stringify(saveData);
            localStorage.setItem(this.storageKey, jsonString);
            
            console.log('游戏数据已保存');
            return true;
        } catch (error) {
            console.error('保存游戏数据失败:', error);
            return false;
        }
    }
    
    // 加载游戏数据
    loadGame() {
        if (!this.isLocalStorageSupported()) {
            return null;
        }
        
        try {
            const jsonString = localStorage.getItem(this.storageKey);
            
            if (!jsonString) {
                console.log('没有找到存档数据');
                return null;
            }
            
            const saveData = JSON.parse(jsonString);
            
            // 验证数据格式
            if (!this.validateSaveData(saveData)) {
                console.warn('存档数据格式无效');
                return null;
            }
            
            console.log('游戏数据已加载');
            return saveData.data;
        } catch (error) {
            console.error('加载游戏数据失败:', error);
            return null;
        }
    }
    
    // 验证存档数据
    validateSaveData(saveData) {
        if (!saveData || typeof saveData !== 'object') {
            return false;
        }
        
        // 检查必要字段
        if (!saveData.version || !saveData.timestamp || !saveData.data) {
            return false;
        }
        
        // 检查数据结构
        const data = saveData.data;
        if (!data.gameData || !data.chickenData) {
            return false;
        }
        
        return true;
    }
    
    // 检查并升级数据版本
    checkAndUpgradeData() {
        const saveData = this.loadRawData();
        
        if (!saveData) {
            return;
        }
        
        const savedVersion = saveData.version || '0.0.0';
        
        if (this.compareVersions(savedVersion, this.version) < 0) {
            console.log(`升级存档数据从 ${savedVersion} 到 ${this.version}`);
            this.upgradeData(saveData, savedVersion);
        }
    }
    
    // 加载原始数据
    loadRawData() {
        try {
            const jsonString = localStorage.getItem(this.storageKey);
            return jsonString ? JSON.parse(jsonString) : null;
        } catch (error) {
            console.error('加载原始数据失败:', error);
            return null;
        }
    }
    
    // 比较版本号
    compareVersions(version1, version2) {
        const v1parts = version1.split('.').map(Number);
        const v2parts = version2.split('.').map(Number);
        
        for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
            const v1part = v1parts[i] || 0;
            const v2part = v2parts[i] || 0;
            
            if (v1part < v2part) return -1;
            if (v1part > v2part) return 1;
        }
        
        return 0;
    }
    
    // 升级数据
    upgradeData(saveData, fromVersion) {
        // 这里可以添加不同版本间的数据升级逻辑
        
        // 示例：从旧版本升级
        if (this.compareVersions(fromVersion, '1.0.0') < 0) {
            // 添加新字段的默认值
            if (saveData.data && saveData.data.gameData) {
                if (!saveData.data.gameData.totalEggs) {
                    saveData.data.gameData.totalEggs = saveData.data.gameData.eggCount || 0;
                }
            }
        }
        
        // 更新版本号
        saveData.version = this.version;
        saveData.timestamp = Date.now();
        
        // 保存升级后的数据
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(saveData));
            console.log('数据升级完成');
        } catch (error) {
            console.error('数据升级失败:', error);
        }
    }
    
    // 清除游戏数据
    clearGame() {
        if (!this.isLocalStorageSupported()) {
            return false;
        }
        
        try {
            localStorage.removeItem(this.storageKey);
            console.log('游戏数据已清除');
            return true;
        } catch (error) {
            console.error('清除游戏数据失败:', error);
            return false;
        }
    }
    
    // 导出游戏数据
    exportGame() {
        const saveData = this.loadRawData();
        
        if (!saveData) {
            return null;
        }
        
        try {
            const exportData = {
                ...saveData,
                exportTime: Date.now(),
                gameTitle: '蚂蚁庄园'
            };
            
            return JSON.stringify(exportData, null, 2);
        } catch (error) {
            console.error('导出游戏数据失败:', error);
            return null;
        }
    }
    
    // 导入游戏数据
    importGame(jsonString) {
        if (!this.isLocalStorageSupported()) {
            return false;
        }
        
        try {
            const importData = JSON.parse(jsonString);
            
            // 验证导入数据
            if (!importData.gameTitle || importData.gameTitle !== '蚂蚁庄园') {
                throw new Error('无效的游戏存档文件');
            }
            
            if (!this.validateSaveData(importData)) {
                throw new Error('存档数据格式无效');
            }
            
            // 移除导入相关字段
            delete importData.exportTime;
            delete importData.gameTitle;
            
            // 更新时间戳
            importData.timestamp = Date.now();
            
            // 保存导入的数据
            localStorage.setItem(this.storageKey, JSON.stringify(importData));
            
            console.log('游戏数据导入成功');
            return true;
        } catch (error) {
            console.error('导入游戏数据失败:', error);
            return false;
        }
    }
    
    // 获取存储使用情况
    getStorageInfo() {
        if (!this.isLocalStorageSupported()) {
            return null;
        }
        
        try {
            const saveData = localStorage.getItem(this.storageKey);
            const dataSize = saveData ? new Blob([saveData]).size : 0;
            
            // 计算localStorage总使用量
            let totalSize = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    totalSize += localStorage[key].length;
                }
            }
            
            return {
                gameDataSize: dataSize,
                totalStorageSize: totalSize,
                gameDataSizeKB: Math.round(dataSize / 1024 * 100) / 100,
                totalStorageSizeKB: Math.round(totalSize / 1024 * 100) / 100,
                lastSaveTime: this.getLastSaveTime()
            };
        } catch (error) {
            console.error('获取存储信息失败:', error);
            return null;
        }
    }
    
    // 获取最后保存时间
    getLastSaveTime() {
        const saveData = this.loadRawData();
        
        if (!saveData || !saveData.timestamp) {
            return null;
        }
        
        return new Date(saveData.timestamp);
    }
    
    // 自动备份
    createBackup() {
        const saveData = this.loadRawData();
        
        if (!saveData) {
            return false;
        }
        
        try {
            const backupKey = `${this.storageKey}_backup_${Date.now()}`;
            localStorage.setItem(backupKey, JSON.stringify(saveData));
            
            // 只保留最近的3个备份
            this.cleanupOldBackups();
            
            console.log('自动备份创建成功');
            return true;
        } catch (error) {
            console.error('创建备份失败:', error);
            return false;
        }
    }
    
    // 清理旧备份
    cleanupOldBackups() {
        try {
            const backupKeys = [];
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(`${this.storageKey}_backup_`)) {
                    backupKeys.push(key);
                }
            }
            
            // 按时间戳排序
            backupKeys.sort((a, b) => {
                const timeA = parseInt(a.split('_').pop());
                const timeB = parseInt(b.split('_').pop());
                return timeB - timeA;
            });
            
            // 删除超过3个的旧备份
            for (let i = 3; i < backupKeys.length; i++) {
                localStorage.removeItem(backupKeys[i]);
            }
        } catch (error) {
            console.error('清理旧备份失败:', error);
        }
    }
    
    // 恢复备份
    restoreBackup(backupKey) {
        try {
            const backupData = localStorage.getItem(backupKey);
            
            if (!backupData) {
                throw new Error('备份不存在');
            }
            
            const saveData = JSON.parse(backupData);
            
            if (!this.validateSaveData(saveData)) {
                throw new Error('备份数据无效');
            }
            
            localStorage.setItem(this.storageKey, backupData);
            
            console.log('备份恢复成功');
            return true;
        } catch (error) {
            console.error('恢复备份失败:', error);
            return false;
        }
    }
}
